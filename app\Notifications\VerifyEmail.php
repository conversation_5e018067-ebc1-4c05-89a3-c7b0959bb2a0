<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\VerifyEmail as BaseVerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Log;

class VerifyEmail extends BaseVerifyEmail
{
    public function toMail($notifiable)
    {
        $verificationUrl = $this->verificationUrl($notifiable);
        // Log only the verification link
        Log::info('Verification link: ' . $verificationUrl);
        // Optionally, you can return a minimal mail message or nothing
        return (new MailMessage)
            ->subject('Email Verification Link')
            ->line('Your verification link has been logged for development:')
            ->line($verificationUrl);
    }
}
