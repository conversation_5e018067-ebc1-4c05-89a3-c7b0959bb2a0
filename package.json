{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "con": "concurrently \"npm run dev\" \"php artisan serve\""}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.0.0", "vite": "^7.0.4"}, "dependencies": {"@inertiajs/react": "^2.0.17", "@vitejs/plugin-react": "^4.7.0", "react": "^19.1.1", "react-dom": "^19.1.1", "ziggy-js": "^2.5.3"}}