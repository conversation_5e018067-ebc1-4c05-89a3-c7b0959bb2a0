import { useF<PERSON>, <PERSON> } from "@inertiajs/react";
import React from "react";

export default function TestValidation() {
    const { data, setData, post, processing, errors } = useForm({
        test_field: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        console.log("Test form submitted with data:", data);
        console.log("Current errors:", errors);
        post("/test-validation");
    };

    console.log("Component rendered with errors:", errors);

    return (
        <>
            <Head title="Test Validation" />
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
                    <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
                        Test Validation
                    </h2>
                    <form onSubmit={handleSubmit} className="w-full flex flex-col gap-3">
                        <input
                            name="test_field"
                            placeholder="Enter at least 10 characters"
                            className="border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-[#0a66c2] bg-[#f3f6f8] text-base"
                            value={data.test_field}
                            onChange={(e) => setData("test_field", e.target.value)}
                        />
                        {errors.test_field && (
                            <div className="text-red-500 text-sm">
                                {errors.test_field}
                            </div>
                        )}
                        <button
                            type="submit"
                            className="bg-[#0a66c2] text-white px-6 py-2 rounded-full font-semibold hover:bg-[#004182] transition"
                            disabled={processing}
                        >
                            {processing ? "Testing..." : "Test Validation"}
                        </button>
                    </form>
                    <div className="mt-4 p-4 bg-gray-100 rounded">
                        <h3 className="font-bold">Debug Info:</h3>
                        <p>Errors object: {JSON.stringify(errors)}</p>
                        <p>Processing: {processing.toString()}</p>
                    </div>
                </div>
            </div>
        </>
    );
}
